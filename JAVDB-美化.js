// ==UserScript==
// @name         JavDB 网站美化
// @description  为 JavDB 网站添加圆角、阴影效果、标签按钮间距调整，以及详细页面优化。
// <AUTHOR>
// @version      2.20
// @match        http*://javdb.com/*
// @run-at       document-start
// @grant        GM_addStyle
// @grant        GM_setValue
// @grant        GM_getValue
// @license      MIT
// ==/UserScript==
(function () {
    "use strict";

// 初始隐藏页面，等待样式加载完成
    const hideCss = `
body { visibility: hidden !important; opacity: 0 !important; transition: opacity 0.3s ease !important; }
body.script-ready { visibility: visible !important; opacity: 1 !important; }
`;
    const hideStyle = document.createElement('style');
    hideStyle.id = "javdb-hide-style";
    hideStyle.textContent = hideCss;
    document.documentElement.appendChild(hideStyle);

// 定义背景选项
    const backgroundOptions = [
        {
            id: 'bg1',
            name: '背景1',
            styles: {
                backgroundImage: 'url(data:image/jpeg;base64,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)',
                filter: 'blur(100px) saturate(180%)',
                opacity: 0.3,
                transform: 'rotate(16deg)',
                backgroundSize: '150%',
                top: '-50%',
                left: '-90%'
            }
        },
        {
            id: 'bg2',
            name: '背景2',
            styles: {
                backgroundImage: 'url(data:image/jpeg;base64,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)',
                filter: 'blur(100px) saturate(380%)',
                opacity: 0.35,
                transform: 'rotate(273deg)',
                backgroundSize: '30%',
                top: '-80%',
                left: '-47%'
            }
        },
        {
            id: 'bg3',
            name: '背景3',
            styles: {
                backgroundImage: 'url(data:image/jpeg;base64,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)',
                filter: 'blur(110px) saturate(200%)',
                opacity: 0.55,
                transform: 'rotate(10deg)',
                backgroundSize: '30%',
                top: '-50%',
                left: '-90%'
            }
        },
        {
            id: 'bg4',
            name: '背景4',
            styles: {
                backgroundImage: 'url(data:image/jpeg;base64,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)',
                filter: 'blur(100px) saturate(150%)',
                opacity: 0.35,
                transform: 'rotate(10deg)',
                backgroundSize: '40%',
                top: '-45%',
                left: '-95%'
            }
        }
    ];

// 获取当前选中的背景索引（如果不存在，则默认为0）
    const currentBgIndex = GM_getValue('javdb_bg_index', 0);

// 创建和插入基础样式（立即执行，不依赖DOM）
    const baseStyle = document.createElement('style');
    baseStyle.id = "javdb-base-style";
    baseStyle.type = 'text/css';
    baseStyle.innerHTML = `
html, body {
    margin: 0;
    background: none !important;
    background-image: none !important;
    position: relative;
}

.gradient-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
    border-radius: 0px !important;
}

.gradient-background::before {
    content: '';
    position: absolute;
    width: 200%;
    height: 200%;
    transition: all 1.2s ease;
    background-repeat: repeat;
    z-index: 1;
    top: ${backgroundOptions[currentBgIndex].styles.top};
    left: ${backgroundOptions[currentBgIndex].styles.left};
    background-size: ${backgroundOptions[currentBgIndex].styles.backgroundSize};
    background-image: ${backgroundOptions[currentBgIndex].styles.backgroundImage};
    filter: ${backgroundOptions[currentBgIndex].styles.filter};
    opacity: ${backgroundOptions[currentBgIndex].styles.opacity};
    transform: ${backgroundOptions[currentBgIndex].styles.transform};
    animation: ${backgroundOptions[currentBgIndex].styles.animation};

}

/* 背景切换器样式 */
.bg-switcher {
    position: fixed;
    bottom: 60px;
    right: 10px;
    display: flex;
    gap: 12px;
    z-index: 9999;
    padding: 8px;
    border-radius: 30px !important;
    background-color: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(5px);
    box-shadow: 0 1px 12px rgba(0, 0, 0, 0);
    transition: all 0.3s ease;
    opacity: 1;
    flex-direction: column;
}

.bg-switcher:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.4);
}

.bg-option {
    width: 12px;
    height: 12px;
    border-radius: 50% !important;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.bg-option:hover {
    transform: scale(1.2);
}

.bg-option.active {
    transform: scale(1.3);
    border: 2px solid #ffffff;
    background: #63CA56 !important;
}

.bg-option::after {
    content: attr(title);
    position: absolute;
    top: -5px;
    right: 10%;
    transform: translateX(0%) scale(0);
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    padding: 2px 8px;
    border-radius: 6px;
    font-size: 10px;
    white-space: nowrap;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
}

.bg-option:hover::after {
    opacity: 1;
    transform: translateX(-50%) scale(1);
}
#bg-option-3, #bg-option-2, #bg-option-1, #bg-option-0 {
    background: rgba(0, 0, 0, 0.20);
}
    `;
    document.documentElement.appendChild(baseStyle);

    // 待页面结构开始形成时，添加背景层
    const addBackgroundLayer = () => {
        if (document.body) {
            if (!document.querySelector('.gradient-background')) {
                const gradientDiv = document.createElement('div');
                gradientDiv.className = 'gradient-background';
                document.body.appendChild(gradientDiv);
            }

            // 添加背景切换器
            if (!document.querySelector('.bg-switcher')) {
                const bgSwitcher = document.createElement('div');
                bgSwitcher.className = 'bg-switcher';

                // 创建4个选项按钮
                backgroundOptions.forEach((option, index) => {
                    const bgOption = document.createElement('div');
                    bgOption.className = `bg-option ${index === currentBgIndex ? 'active' : ''}`;
                    bgOption.id = `bg-option-${index}`;
                    bgOption.setAttribute('title', option.name);

                    // 添加点击事件
                    bgOption.addEventListener('click', () => {
                        // 更新活动状态
                        document.querySelectorAll('.bg-option').forEach(opt => opt.classList.remove('active'));
                        bgOption.classList.add('active');

                        // 更新背景样式
                        updateBackground(index);

                        // 保存选择
                        GM_setValue('javdb_bg_index', index);
                    });

                    bgSwitcher.appendChild(bgOption);
                });

                document.body.appendChild(bgSwitcher);
            }

            return true;
        }
        return false;
    };

    // 更新背景样式
    const updateBackground = (index) => {
        // 查找所有样式表
        let gradientRule = null;
        for (let i = 0; i < document.styleSheets.length; i++) {
            try {
                const rules = document.styleSheets[i].cssRules || document.styleSheets[i].rules;
                for (let j = 0; j < rules.length; j++) {
                    if (rules[j].selectorText && rules[j].selectorText.includes('.gradient-background::before')) {
                        gradientRule = rules[j];
                        break;
                    }
                }
                if (gradientRule) break;
            } catch (e) {
                // 跨域样式表会抛出安全错误，忽略
                continue;
            }
        }

        if (gradientRule) {
            gradientRule.style.backgroundImage = backgroundOptions[index].styles.backgroundImage;
            gradientRule.style.filter = backgroundOptions[index].styles.filter;
            gradientRule.style.opacity = backgroundOptions[index].styles.opacity;
            gradientRule.style.animation = backgroundOptions[index].styles.animation;
            gradientRule.style.transform = backgroundOptions[index].styles.transform;
            gradientRule.style.backgroundSize = backgroundOptions[index].styles.backgroundSize;
            gradientRule.style.top = backgroundOptions[index].styles.top;
            gradientRule.style.left = backgroundOptions[index].styles.left;
        } else {
            // 如果找不到规则，直接操作DOM
            const gradientBg = document.querySelector('.gradient-background::before');
            if (gradientBg) {
                gradientBg.style.backgroundImage = backgroundOptions[index].styles.backgroundImage;
                gradientBg.style.filter = backgroundOptions[index].styles.filter;
                gradientBg.style.opacity = backgroundOptions[index].styles.opacity;
                gradientBg.style.animation = backgroundOptions[index].styles.animation;
                gradientBg.style.transform = backgroundOptions[index].styles.transform;
                gradientBg.style.backgroundSize = backgroundOptions[index].styles.backgroundSize;
                gradientBg.style.top = backgroundOptions[index].styles.top;
                gradientBg.style.left = backgroundOptions[index].styles.left;
            }
        }
    };

    // 尝试添加背景层，如果DOM还未准备好，则设置观察器
    if (!addBackgroundLayer()) {
        const bodyObserver = new MutationObserver(() => {
            if (addBackgroundLayer()) {
                bodyObserver.disconnect();
            }
        });
        bodyObserver.observe(document.documentElement, { childList: true, subtree: true });
    }

    // 样式内容
    const customStyles = `
*  {
    --border-line: 1px solid rgba(255, 255, 255, 0.4);
    --background-color: rgba(255, 255, 255, 0.5);
    --outline-rgba: rgba(0, 0, 0, 0.1);
}

strong {
    font-weight: 700 !important;
}

/* 全局背景色 */
body {
    text-decoration: none !important;
}

/* 全局圆角 */
* {
    border-radius: 10px !important;
}

/* 全局卡片白底 */
.item {
    background-color: rgba(255, 255, 255, 0.8) !important;
}

/* 全局按钮 */
.button {
background-color: rgba(255, 255, 255, 0.8);
border-color: #fff;
border-width: 1px;
border-radius: 10px !important;
color: #888;
}

/* 全局按钮（hover） */
.button:hover {
	border-color: rgba(255, 255, 255, 0.8) !important;
}

/* 全局按钮点击效果 */
.button.is-info:active {
    transform: scale(1.5);
}

.modal-card-body,
.modal-card-foot, .modal-card-head {
	border-radius: 0px !important;
}

/*  ---- ---- ---- ----顶部导航栏 ---- ---- ---- ---- */
.navbar {
  position: relative;
  background-color: rgba(244, 58, 75, 0.5) !important;
  border-radius: 0px !important;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2) !important;
}

.navbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: inherit;
  backdrop-filter: blur(20px);
  z-index: -1;
  border-radius: 0px;
}

.navbar-dropdown.is-right, .navbar-dropdown.is-boxed, .navbar.is-spaced .navbar-dropdown {
	box-shadow: 0 1px 30px rgba(0, 0, 0, 0.2) !important;
	background: rgba(255, 255, 255, 0.7) !important;
	backdrop-filter: blur(20px) saturate(180%) !important;
}

.navbar-dropdown {
    border-top: none;
}

.document.querySelector {
   background-color: #CE5966 !important;
}

/* 鼠标悬停时改变下拉菜单项的背景色 */
.navbar-item.has-dropdown:hover .navbar-dropdown .navbar-item:hover {
	background-color: transparent !important;
	border-radius: 0px !important;
	color: red;
}

/* SVG 修改 */
rect[fill="#2F80ED"] {
    fill: transparent !important;
}

/* 隐藏广告 */
.list-tab,
.navbar-item[href='/rankings/playback'],  /* 热播 */
.movie-list .item .cover .tag-can-play.cnsub, /* 中字可播放 */
.sub-header, /* 广告 */
.navbar.is-dark.is-fluid {
    display: none !important;
}

/* 去除蓝色线 */
.has-left-sep {
    border: none !important;
}

/* 背景 */
.navbar.is-blackcusbar,
.navbar.is-blackcusbar:hover,
.navbar.is-black:hover {
    background-color: #e45e6b !important;
}
.navbar-dropdown a.navbar-item,
.navbar-dropdown .navbar-item {
  padding: 0 !important;
  text-align: center !important;
  display: flex;
  flex-direction: column;
  align-items: center;
}

#navbar-menu-hero > div > div:nth-child(n+1) > div {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 10px 15px 10px;
    gap: 10px;
    position: absolute;
    left: 37%;
    top: 100%;
    transform: translate(-50%, 0%);
}

.navbar-dropdown:hover a.navbar-item:hover,
.navbar-dropdown:hover .navbar-item:hover {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.navbar-item:hover, .navbar-item.has-dropdown:hover .navbar-link {
  background-color: #e45e6b !important;
  border-radius: 8px !important;
}

.navbar-dropdown .navbar-item,
.navbar-dropdown a.navbar-item {
  padding-right: 0rem !important;
  padding-left: 0rem !important;
}

.navbar-dropdown.is-right {
  display: none;  /* 默认隐藏菜单 */
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding: 10px;
  position: absolute;  /* 让菜单浮动 */
}

.navbar-item.has-dropdown:hover .navbar-dropdown.is-right {
  display: flex;  /* 鼠标悬停时显示菜单 */
}

.navbar.is-black .navbar-end a.navbar-item:focus,
.navbar.is-black .navbar-end .navbar-link:focus,
.navbar.is-black .navbar-start a.navbar-item:focus,
.navbar.is-black .navbar-start .navbar-link:focus,
.navbar.is-black .navbar-brand a.navbar-item:focus {
  background-color: transparent;
}

/* ---- ---- ---- ---- 搜索---- ---- --- ---- -------*/
 /* 影片和小箭头（左边）*/
        /* 下拉框样式 */
        .search-bar-wrap .search-type select {
            height: 40px !important;
            border: none !important;
            color: #7367B6 !important;
        }
        /* 小箭头 */
        .select:not(.is-multiple):not(.is-loading):after {
            border-color: #7367B6 !important;
            right: 1.125em;
            z-index: 4;
        }
        /* 鼠标悬停时改变箭头颜色 */
        .select:not(.is-multiple):not(.is-loading):hover:after {
            border-color: #7367B6 !important;
        }
        /* 搜索框背景 */
        .search-bar-wrap {
            background-color: rgba(156, 100, 219, 0.6) !important;
            padding: 0.8rem;
            box-shadow: 0 1px 12px rgba(0, 0, 0, 0.1) !important;
            margin-top: 30px !important;
            height: 70px;
        }
        /* 搜索框内输入框样式 */
        .search-bar-wrap .search-input .input {
            box-shadow: none !important;
            border: none !important;
            height: 40px;
        }
        /* 控件整体间距 */
        .search-bar-wrap .control {
            display: flex !important;
            align-items: center !important;
            transform: translateY(-4px);
            margin: 4px !important;
        }
/* 检索按钮组 */
.search-bar-wrap .control .button {
	background-color: rgba(156, 100, 219, 0.6) !important;
	margin: 2px !important;
	height: 40px !important;
	border: none;
}
/* 鼠标悬停按钮效果 */
.search-bar-wrap .control .button:hover {
	background-color: #DB5266 !important;
	border: none !important;
}
        /* 弹出气泡 */
        .tooltip::before {
            border-radius: 8px !important;
            top: -5px !important;
            box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2) !important;
        }
        /* 删除泡泡小箭头 */
        [data-tooltip]:not(.is-disabled):after,
        [data-tooltip]:not(.is-loading):after,
        [data-tooltip]:not([disabled]):after {
            content: none !important;
        }
/* ----猜你喜欢，全部，有码，无码 ----  ---- ------- ---- --- ---- -------*/
      /* 按钮样式 */
        .pagination-link.is-current,
        .float-buttons a {
            color: #fff !important;
            background-color: #AC86C5 !important; /* 设置背景颜色为紫色 */
            border: none !important; /* 无边框*/
            box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1) !important;
        }
         /* 激活按钮的样式 */
        .tabs li.is-active a {
           color: #fff !important;
           border: none !important; /* 无边框 */
           box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1) !important;
           background-color: rgba(156, 100, 219, 0.6) !important; /* 设置背景颜色为紫色 */
        }
        /* 激活按钮的鼠标悬停样式（不改变任何效果） */
        .tabs li.is-active a:hover {
          color: #fff !important; /* 保持文字颜色 */
          background-color: #AC86C5 !important; /* 保持背景颜色 */
        }
        /* 鼠标悬停时的样式（其他按钮） */
        .tabs li a:hover {
          color: #AC86C5 !important; /* 鼠标悬停时的文字颜色 */
        }
        /* 按钮*/
        .tabs.main-tabs.is-boxed {
            margin-left: -18px !important; /* 左对齐*/
            margin-bottom: 10px !important;/* 上下间距*/
        }
/* ----删除  -------*/
        /* 移除tab底部边框 */
        .tabs ul {
            border-bottom: none !important;
        }
        /* 移除主页顶部和详细页tab蓝色分隔线 */
        .tabs.is-boxed,
        .tabs.no-bottom {
            border-bottom: 0 !important;
            margin-bottom: 10px !important;
        }
/* ----大封面，小封面 ----  ---- ------- ---- --- ---- -------*/
       /* 按钮组 hover 样式 */
.buttons.has-addons :hover {
	color: #Ed7676 !important;
}
.button.is-active, .button:active {
	border-color: #fff;
	color: #363636;
}
.button.is-focused:not(:active), .button:focus:not(:active) {
	box-shadow: none;
}
/* 激活按钮 */
        .buttons.has-addons .button.is-selected {
            background-color: #Ed7676 !important; /* 默认背景颜色 */
            border-color: #Ed7676; /* 默认边框颜色 */
            border-width: 1px;
            color: #fff; /* 默认字体颜色 */
        }
        /* 鼠标悬停时的样式 */
        .buttons.has-addons .button.is-selected:hover {
            color: #fff !important; /* 鼠标悬停时的字体颜色 */
            border-color: #Ed7676 !important; /* 鼠标悬停时的边框颜色 */
            background-color: #Ed7676; /* 保持背景颜色不变（可选） */
        }
      /* 按钮组样式调整 */
        .buttons.has-addons > .button {
            margin: 0 !important; /* 去除按钮之间的间距 */
            border-radius: 0 !important; /* 移除单独按钮的圆角 */
         }
        /* 调整按钮组首尾按钮圆角 */
        .buttons.has-addons > .button:first-child {
            border-top-left-radius: 8px !important;
            border-bottom-left-radius: 8px !important;
        }
        .buttons.has-addons > .button:last-child {
            border-top-right-radius: 8px !important;
            border-bottom-right-radius: 8px !important;
            border-left-width: 1px !important; /* 确保最后一个按钮和前一个按钮显示正常边框 */
        }
        .buttons.has-addons > .button:not(:first-child) {
            border-left: none !important; /* 移除中间按钮的左边框 */
            /* border-right: none !important; /* 移除中间按钮的左边框 */
        }
/*  ---- ---- ---- ---- ---- 主页更新页面 ---- ---- ---- ---- ----*/
 /* 1.卡片 */
        /*整体布局*/
        .item {
            border: 0px solid #fff !important; /* 设置边框 */
            margin-left: 0 !important; /* 左对齐 */
            margin-right: 0 !important; /* 右对齐 */
            transform: translateY(10px); /* 向下移动 */
        }

.box {
	background-color: transparent;
	border: none !important;
}
        /*卡片间距*/
        .movie-list {
            display: grid;
            grid-column-gap: .4rem;
            -moz-column-gap: .4rem;
            column-gap: 1.2rem;
            grid-row-gap: 1rem;
            row-gap: 1.6rem;
            grid-template-columns: repeat(4,minmax(0,1fr));
            padding-bottom: .5rem;
        }
        /* 鼠标指向卡片边框 */
        a.box:focus, a.box:hover {
           outline: none;
           box-shadow: none;
           }
         .item:focus, .item:hover {
           outline: 4px solid #Ed7676; /* 修改为橙色边框 */
           box-shadow: 0 0 12px 5px rgba(238, 149, 149, 0.5);
         }
/* 2.封面 */
          /* 封面填充 */
          .movie-list .item .cover.contain img {
              width: 100%;
              object-fit: cover;
          }
         /* 封面圆角 */
        .item .cover img {
            border-bottom-left-radius: 0 !important;  /* 默认移除底部圆角 */
            border-bottom-right-radius: 0 !important; /* 默认移除底部圆角 */
        }
        /* 封面图背景 */
          .movie-list .item .cover {
              background: #fff;
              border-bottom-left-radius: 0 !important;  /* 默认移除底部圆角 */
              border-bottom-right-radius: 0 !important; /* 默认移除底部圆角 */
          }
.movie-list .item .cover .tag-can-play {
	font-size: .7rem !important;
	padding: 1px 4px  !important;
	border-radius: 8px !important;
	margin: 0 5px 5px !important;
	box-shadow: 0 1px 6px rgba(0, 0, 0, 0.3) !important;
}
        /* 鼠标悬停状态*/
        .item .cover img:hover {
            border-radius: 8px !important; /* 保证放大时四个角有圆角 */
            border-bottom-left-radius: 8px !important;  /* 恢复底部左角圆角 */
            border-bottom-right-radius: 8px !important; /* 恢复底部右角圆角 */
        }
        .item .video-title {
            border-radius: 0 !important;  /* 免疫标题部分的圆角 */
        }
/* 3.标题 */
        /* 修改未访问链接的颜色 */
        .movie-list .box .video-title {
            color: #444444 !important;  /* 修改未访问链接的标题颜色为灰色 */
        }
        /* 两行显示*/
        .movie-list .item .video-title {
            font-size: 14px !important;               /* 设置字体大小 */
            padding-top: 0.2rem;                      /* 控制顶部间距 */
            padding-bottom: 0.2rem;                   /* 控制底部间距 */
            white-space: normal;                      /* 允许换行 */
            overflow: hidden;                         /* 隐藏超出容器的内容 */
            text-overflow: ellipsis;                  /* 超出部分显示省略号 */
            line-height: 1.5rem;                      /* 每行文字高度 */
            height: 3.5rem;                           /* 设置容器高度为两行文字略多 */
            display: -webkit-box;                     /* Flex 布局，支持多行截断 */
            -webkit-box-orient: vertical;             /* 垂直布局 */
            -webkit-line-clamp: 2;                    /* 限制最多显示两行 */
         }
/* 4.评分 */
         .movie-list .item .score {
            padding-top: 0.4rem;                 /* 设置顶部边距 */
            padding-bottom: .2rem;              /* 设置底部边距 */
            color: #333;
            font-size: 12px;
          }
/* 5.日期 */
          .movie-list .item .meta {
              padding-bottom: .2rem;
              color: #333;
              font-size: 12px;
          }
/* 6.标签 */
       /* 标签间距 */
        .tags.has-addons .tag, .tags.is-right .tag:not(:last-child) {
            margin-right: 10px; /* 恢复默认值 */
        }
.tag:not(body).is-info {
	background-color: #3e8ed0  !important;
	color: #fff;
}

#tags dt.collapse {
	height: 45px;
}
.button.is-info.is-outlined {
color: #fff !important;
	border-color: transparent  !important;
}
/* 含中字磁链 */
.tag.is-warning {
    color: #fff !important; /* 设置字体颜色 */
    background-color: #f4b16b !important; /* 背景色为黑色 */
}
.button.is-info.is-focused:not(:active), .button.is-info:focus:not(:active) {
	box-shadow: none;
}
/* 昨日新种 */
.tag:not(body) {
    background-color: rgba(0, 0, 0, 0.20) !important;
    border-radius: 8px !important;
    color: #fff;
}
.tag:not(body).is-success {
	background-color: #48c78e !important;
	color: #fff;
}
.tags .tag {
    margin-bottom: .3rem;
    border-radius: 8px !important;
}

/*  ---- 底部翻页按钮----*/
/* 去横线 */
nav.pagination {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 0px solid hsla(0,0%,85.9%,.5);
}

/* 底部当前页激活时的翻页按钮颜色 */
.pagination-link.is-current:active {
  background-color: transparent;
  border: none !important;

}

/* 底部按钮（还包括部分顶部按钮） */
.pagination-ellipsis, .pagination-link, .pagination-next, .pagination-previous, .select select, .textarea {
    color: #856599 !important;
    border: none;
    font-size: .85em;
}

.pagination-link, .pagination-next, .pagination-previous {
      background-color: transparent;
}

/* 去掉省略号阴影 */
.pagination-ellipsis {
    box-shadow: none !important;
}

/* 底部按浮动按钮 */
.float-buttons {
    padding: 0.2rem!important;
    display: flex!important;
    gap: 10px!important;
}

.movie-list .item .cover.contain img, .movie-list .item .cover img {
    transition: transform .5s ease;
}

.pagination-link:active, .pagination-next:active, .pagination-previous:active {
    box-shadow: none;
    color: #E45E6B !important;
}

.app-desktop-banner .container {
    display: none !important;
}

/* ---- ---- ---- ---- ---- --- ---- ---- ---2.详细页 ---- ---- --- ---- ---- --- ---- ---- ---- -------*/
/*  ---- ----2.0 封面 ---- ---- -*/
.column-video-cover {
	padding: 1.5rem;
}
        .column-video-cover img {
            outline: 6px solid var(--outline-rgba) !important;
            margin-top: 5px;
        }
/*  ---- ---- ---- ---- 2.1 封面右边详细信息---- ---- ---- ---- -*/
/* 卡片背景色 */
        .message-body {
            background-color: transition !important;
        }
/* 行高 */
        .movie-panel-info .panel-block {
            line-height: 1.5;     /* 设置行内间距 */
            margin-bottom: 10px; /* 设置行间距 */
        }
        .movie-panel-info .panel-block:last-child {
            margin-bottom: 0; /* 移除最后一行的额外间距 */
        }
/* 隐藏指定的元素 */
        /* 去分隔线 */
        .panel-block {
            border-bottom: none !important; /* 番号，日期，时长，片商 */
        }
        /* 其他 */
        .buttons.are-small.review-buttons,  /* 下载，修正 */
        .play-button,
        .list-tab,
        .is-size-7.has-text-grey, /* 多少人想看 */
        .sub-header, /* 广告 */
        .top-meta, /* 官方app，telegram频道 */
        .search-recent-keywords,
        .navbar.is-dark.is-fluid {
            display: none !important;
        }
/* 右边文字 */
        /* 复制按钮 */
       .button.is-white.copy-to-clipboard {
           box-shadow: none !important; /* 去阴影 */
         }
       .button.is-white.copy-to-clipboard:hover{
           background-color: transparent !important; /* hover 背景色 */
         }
         .button.is-white {
	background-color: transparent;
}
/* 其他 */
       .panel.movie-panel-info .panel-block {
           font-size: 14px !important; /* 示例字体大小 */
         }
        .panel.movie-panel-info .panel-block a {
            color: #333 !important; /* 超链接的颜色 */
        }
        .panel.movie-panel-info a:hover {
            color: #ef9595 !important; /* 修改鼠标悬停时的超链接颜色为浅番茄红 */
        }
        .score-stars [class^=icon-] {
            color: #ED7676 !important; /* 星星得分 */
        }
.score-stars .gray {
	color: rgba(0, 0, 0, 0.20) !important;
}
.video-panel .magnet-links .item .magnet-name .name {
	font-weight: 600 !important;
	font-size: 14px !important;
}
/*  ---- ---- ---- ---- 2.2 预览图框格---- ---- ---- ---- ----*/
  /*整体布局*/
.preview-images {
	display: grid !important;
	grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)) !important;
	grid-gap: 20px !important;
}
        /* 确保每个框格的图片按原始比例展示，且黑色填充空白 */
.preview-images img {
	width: 100% !important;
	height: 100% !important;
	object-fit: cover !important;
	background-color: var(--background-color) !important;
	display: block !important;
	aspect-ratio: 16 / 10 !important;
	outline: 4px solid var(--outline-rgba) !important;
}
        /* 确保每个框格的容器用黑色背景填满整个区域 */
        .preview-images .item {
            background-color: black !important; /* 背景色为黑色 */
            display: flex !important; /* 使用flex布局来让图片居中 */
            justify-content: center !important; /* 水平居中 */
            align-items: center !important; /* 垂直居中 */
            height: 100% !important; /* 确保框格的高度填满容器 */
            width: 100% !important; /* 让图片宽度填满框格 */
        }
 /* 视频预览 */
        /* 修改 preview-video-container 字体大小和颜色 */
        .preview-video-container span {
            font-size: 11px !important; /* 设置字体大小 */
            border-radius: 3px !important;
            color: #FFFFFF !important; /* 设置字体颜色 */
            position: absolute !important; /* 按钮绝对定位 */
            top: 10% !important; /* 相对于父容器的垂直居中 */
            left: 5% !important; /* 距离左侧 */
        }
         /* 去掉视频预览遮罩 */
        .preview-video-container:after {
            background-color: rgba(0,0,0,.0);
        }
      .tag.is-dark {
                  background-color:#000 !important; /* 背景色为黑色 */
              }
      .tag.is-warning.tooltip {
                  background-color:#ff90bc !important; /* 背景色为黑色 */
              }
/*  ---- ---- ---- ---- ---- 2.3 下载页 ---- ---- ---- ---- ----*/
        /* 移除边框残影 */
        .tabs a {
            border-bottom: none !important;
            margin-bottom: 0px;
        }
       /* 列居中 */
        .item.columns.is-desktop,
        .item.columns.is-desktop.odd {
            margin: 6px !important;
            transform: translateY(-4px); /* 向下移动按钮组 10 像素 */
        }
        .item.columns.is-desktop:hover,
        .item.columns.is-desktop.odd:hover {
            background-color: #fff !important;
        }
        /* 按钮组 */
        .tabs.is-boxed, .tabs.no-bottom {
            border-bottom: 0 !important;
            margin-bottom: 0px !important;
            overflow: visible;
        }
        /* 背景透明 */
        .tabs.is-boxed a {
            background-color: transparent !important;
        }
        /* name、meta 和 tags 列整体右移 20px */
        .magnet-links .item .magnet-name,
        .magnet-links .item .magnet-meta,
        .magnet-links .item .magnet-tags {
            margin-left: 10px !important; /* 向右移动 20px */
        }
         /* 高清 */
        .tag.is-primary.is-small.is-light {
            color: #fff !important; /* 设置字体颜色 */
            background-color: #49C78E !important; /* 背景色为黑色 */
        }
          /* 字幕 */
        .tag.is-warning.is-small.is-light {
            color: #fff !important; /* 设置字体颜色 */
            background-color: #f4b16b !important; /* 背景色 */
        }
          /* meta(大小，文件个数）) */
        .video-panel .magnet-links .item .magnet-name .meta {
            color: #888888 !important; /* 设置字体颜色 */
            font-size: 11px !important;
        }
        /* 磁力链接按钮位置，颜色 */
        .magnet-links .item .buttons {
            position: absolute !important; /* 按钮绝对定位 */
            top: 50% !important; /* 相对于父容器的垂直居中 */
            right: 120px !important; /* 距离右侧调整至 120px，可根据需要手动调整 */
            transform: translateY(-50%) !important; /* 确保垂直方向真正居中 */
            display: flex !important; /* 使用 flex 布局方便调整间距 */
            gap: 0px !important; /* 复制和下载按钮之间的间距，调整为 10px */
            color: #888888 !important; /* 设置字体颜色 */
        }
 /*----复制，下载 ----*/
          /*按钮（复制，下载，赞，收藏）  */
          .button.is-info {
              background-color: #Ed7676 !important;
              color: #fff;
              transition: transform 0.8s ease;
          }
          /*按钮居中 */
          .buttons.column {
              display: flex;
              flex-wrap: wrap; /* 确保按钮不重叠，并且能换行 */
              justify-content: flex-start; /* 按钮的排列方式 */
              align-items: center; /* 按钮垂直居中 */
          }
          .buttons.column > .button.is-info.is-small {
              color: #fff;
              margin: 6px;
              border: none !important;
          }
          .button.is-info.is-small:hover {
            border: none !important;
            color: #fff;
          }
          .video-panel .magnet-links .item .magnet-name a {
            text-decoration: none;
            display: block;
            padding: 0 265px 0 0;
          }
        /*按钮（复制，下载，赞，收藏）  */
        .button.is-info.is-small:hover {
            border-color: transparent;
            color: #fff;
        }
        /* 日期 */
        .magnet-links .item .date {
            position: absolute !important; /* 日期列绝对定位 */
            top: 50% !important; /* 垂直居中 */
            right: 15px !important; /* 距离右侧调整至 10px */
            transform: translateY(-50%) !important; /* 确保垂直方向真正居中 */
        }
        /* 短评 */
        .review-items .review-item {
            padding: .8rem 0;
            border-bottom: none;
            border-radius: 0px !important; /* 保证图片顶部圆角 */
            font-size: 14px !important;
        }
        /* 隐藏更多短评 */
        .review-item.more.has-text-link {
              display: none !important;
        }
/*  -------- ---- ---- ----  2.5 其他---- ---- ---- ----  ----*/
/* header颜色 */
        .video-panel .message-header {
            background-color: transparent !important;
            color: #4a4a4a;
            font-size: 15px;
            border-radius: 6px !important; /* 保证放大时四个角有圆角 */
            border-bottom-left-radius: 0px !important;
            border-bottom-right-radius: 0px !important;
        }
/* 链接颜色 */
        .video-panel .tile-images .tile-item .video-number, .video-panel .tile-images .tile-item .video-title {
            color: #888 !important;
            font-size: .7rem;
        }
/* 卡片化 */
  .tile-images.tile-small .tile-item  {
    border: 0px solid #ccc; /* 添加浅灰色边框 */
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1) !important; /* 阴影*/
    padding: 10px;         /* 内部内容的间距 */
    background-color: #fff; /* 可选，背景颜色 */
  }
/* ---- ---- ---- ---- ---- --- ---- ---- ---3.女优主页---- ---- --- ---- ---- --- ---- ---- ---- -------*/
/* 头像 */
     .avatar {
            object-fit: cover !important; /* 强制图片填满框格 */
            background-color: #Ed7676 !important; /* 背景色为黑色 */
            display: block !important; /* 图片作为块级元素显示 */
            aspect-ratio: 16 / 16 !important; /* 设置 16:9 的固定长宽比 */
            box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2) !important;  /* 阴影效果 */
            outline: 5px solid var(--outline-rgba) !important;
            border-radius: 12px !important; /* 保证图片顶部圆角 */
        }
        .has-text-justified {
            font-size: 1.5rem;
            text-align: justify!important;
            margin-left: 16px;
}
/* 收藏,twitter,instagram. */
          /*按钮左对齐，增加间距*/
          .column.section-addition > .field.has-addons {
              display: flex;        /* 启用 Flexbox 布局 */
              align-items: center;  /* 垂直方向居中对齐 */
              justify-content: flex-start; /* 保证按钮组左对齐 */
          }
          .column.section-addition > .field.has-addons > .control:not(:first-child) {
              margin-left: 10px; /* 从第二个按钮开始设置水平间距 */
          }
        /* 心形图标 */
        .icon-heart-o {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            width: 20px;
            height: 20px;
        }
        /* twitter */
        a.button.is-info[href*="twitter.com"] {
            background-color: #028FD6 !important;
        }
        /* instagramr */
        a.button.is-info[href*="instagram.com"] {
            background-color: #b547a3 !important;
        }
/* 标签组 */
        .actor-tags.tags {
            border: none; !important; /* 移除tag底部边框 */
            transform: translateY(20px); /* 向下移动 */
        }
 /* 下移 */
        .actor-tags .collapse {
            height: 30px !important;
        }
 /* 标签文字大小…（全局）  */
        .tag:not(body).is-medium {
            font-size: 0.8rem;
            box-shadow: none !important;  /* 阴影效果 */
        }
 /* "全部"*/
        .tag:not(body).is-link {
            background-color: #AC86C5 !important;
            color: #fff !important;
        }
         div.actor-tags.tags > div > a.button.is-link.tag-expand > span.text:hover {
            color: #fff !important;
}
 /* "可播放，单体作品……"  */
.actor-tags .tag {
	color: #fff !important;
	background-color: rgba(0, 0, 0, 0.20) !important;
}

.actor-tags .tag:hover, .actor-tags .button:hover {
	text-decoration: none !important;
	background-color: rgba(0, 0, 0, 0.8) !important;
}
.tag.is-medium.is-link:hover,
.button.is-link.tag-expand.is-outlined:hover,
.button.is-link.tag-expand:hover {
 color: #fff !important;
}
.tag:not(body) .delete {
   border-radius: 50% !important;
}
.tags .tag:not(:last-child) {
   margin-right: .4rem;
}
/* 更多 */
        /* 按钮大小 */
        .actor-tags .content .tag-expand {
            padding: calc(.9em - 6px) 0.5em;
            font-size: .75rem;
            float: right;
        }
        /* 按钮颜色 */
        .button.is-link.is-outlined,
        .button.is-link {
           background-color:#AC86C5 !important;
           border: none; !important;
           color: #fff !important;
        }
        .button.is-link.is-outlined.tag-expand:hover {
            background-color: #AC86C5 !important; /* 设置背景色为紫色 */
            color: white !important; /* 设置文字颜色为白色 */
        }
/* 隐藏male元素 */
.hidden {
    display: none !important;
}
}
/* ---- ---- ---- ---- ---- --- ---- ---- ---4.女优主页---- ---- --- ---- ---- --- ---- ---- ---- -------*/
#tags dt a.tag-expand {
    float: right;
    margin-top: .6rem;
    font-size: .6rem;
    padding: 2px 5px;
    border-color: #e45e6b !important;
    color: #fff;
}
#tags dt {
	border-bottom: none;
}
.actors .box img {
    min-width: 3rem;
    height: auto;
    transition: all .2s ease-in-out;
    box-shadow: none !important;
    border: none !important;
    border-radius: 0 !important;
}
.actors .box {
	overflow: hidden !important;
	box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
	background: rgba(255, 255, 255, 0.8);
}
.actor-box a:visited strong {
    color: #ac86c5 !important;
}
.actor-box a strong {
	padding-top: .6rem;
	padding-bottom: .4rem;
	display: block;
	line-height: 1rem;
	overflow-x: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	color: #DB7094;
}
body > section > div > h3:nth-child(6) {
    font-size: 1.5rem;
    margin-top: 16px;
}
.video-meta-panel {
	background: var(--background-color);
  border: var(--border-line) !important;
}
.panel {
	background: none;
}
.message {
	background-color: var(--background-color);
  border: var(--border-line) !important;
  box-shadow: 0 1px 20px rgba(0, 0, 0, 0.03) !important;
  overflow: hidden;

}
.item.columns.is-desktop, .item.columns.is-desktop.odd {
	background: rgba(255, 255, 255, 0.6) !important;
  border: var(--border-line) !important;
  box-shadow: none !important;
  margin: 10px 6px !important;
  transition: all 0.2s ease !important;
}
.item.columns.is-desktop:hover, .item.columns.is-desktop.odd:hover {
	background-color: rgba(255, 255, 255, 0.8) !important;
  border: var(--border-line) !important;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05) !important;
	outline: none;
}
.tile-images.tile-small .tile-item {
	border: var(--border-line);
	box-shadow: 0 1px 15px rgba(0, 0, 0, 0.04) !important;
	padding: 4px;
	background-color: var(--background-color);
	margin: 0 5px;
}
#reviews,
#magnets {
	margin-top: 4px;
}
.item {
	background-color: rgba(255, 255, 255, 0.8) !important;
  box-shadow: 0 1px 15px rgba(0, 0, 0, 0.05) !important;
}
.item:focus, .item:hover {
	outline: 4px solid #Ed7676;
	box-shadow: 0 0 12px 5px rgba(238, 149, 149, 0.5);
}
.movie-list .item .cover:hover img {
	border-radius: 12px !important;
	box-shadow: 0 1px 30px rgba(0, 0, 0, 0.20) !important;
	outline: 3px solid rgba(245, 245, 245) !important;
}
.movie-panel-info div.panel-block span.value {
	color: #333;
}
.section-container .box {
	background: rgba(255, 255, 255, 0.5) !important;
	border: var(--border-line) !important;
  color: #333;
}
    `;

    // 公共的自动展开功能
    function autoExpandContent() {
        document.querySelectorAll(".replace_tip").forEach(function (replaceTip) {
            const iconExpand = replaceTip.querySelector(".icon-expand");
            if (iconExpand) replaceTip.click();
        });
    }

    // 函数：替换 span.meta 中的逗号为空格
    function replaceCommasInMeta() {
        const metaSpans = document.querySelectorAll("span.meta");
        metaSpans.forEach((span) => {
            if (span.textContent && span.textContent.includes(",")) {
                span.textContent = span.textContent.replace(/,/g, " ");
            }
        });
    }

    // 函数：隐藏包含 'male' 符号的元素及其前一个链接
    function hideMaleElements() {
        document
            .querySelectorAll("strong.symbol.male:not(.hidden)")
            .forEach((element) => {
                const previousLink = element.previousElementSibling;
                if (previousLink && previousLink.tagName === "A") {
                    previousLink.classList.add("hidden");
                }
                element.classList.add("hidden");
            });
    }

    // 图片预览功能 - 使用全局标记避免重复初始化
    function initImagePreview() {
        try {
            // 避免重复初始化 - 检查是否已存在预览窗口
            if (document.getElementById('javdb-image-preview')) {
                console.log('JavDB美化: 预览窗口已存在，跳过初始化');
                return;
            }

            // 检查是否在正确的页面（包含预览图片的页面）
            // 查找所有小图（包含_s_的图片）
            const previewImages = document.querySelectorAll('img[src*="_s_"]');
            if (previewImages.length === 0) {
                console.log('JavDB美化: 未找到预览图片');
                return;
            }

            // 限制处理的图片数量，避免性能问题
            if (previewImages.length > 20) {
                console.warn('JavDB美化: 预览图片数量过多，跳过初始化');
                return;
            }

            console.log(`JavDB美化: 找到 ${previewImages.length} 张预览图片`);
        } catch (error) {
            console.error('JavDB美化: 图片预览初始化检查失败', error);
            return;
        }

        // 移除已存在的预览窗口
        const existingPreview = document.getElementById('javdb-image-preview');
        if (existingPreview) {
            existingPreview.remove();
        }

        // 创建预览窗口
        const previewContainer = document.createElement('div');
        previewContainer.id = 'javdb-image-preview';
        previewContainer.style.cssText = `
            position: fixed !important;
            z-index: 999999 !important;
            background: rgba(0, 0, 0, 0.9) !important;
            border-radius: 8px !important;
            padding: 10px !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5) !important;
            pointer-events: none !important;
            opacity: 0 !important;
            transition: opacity 0.3s ease !important;
            max-width: 600px !important;
            width: 600px !important;
            height: auto !important;
            display: none !important;
            border: 2px solid #fff !important;
        `;

        const previewImg = document.createElement('img');
        previewImg.style.cssText = `
            width: 100% !important;
            height: auto !important;
            border-radius: 4px !important;
            display: block !important;
            max-width: 600px !important;
            max-height: 600px !important;
            object-fit: contain !important;
        `;

        previewContainer.appendChild(previewImg);
        document.body.appendChild(previewContainer);

        console.log('JavDB美化: 预览窗口已创建', previewContainer);

        // 重新获取图片元素并为其添加hover事件
        const currentPreviewImages = document.querySelectorAll('img[src*="_s_"]');
        currentPreviewImages.forEach(img => {
            // 检查是否已经添加过事件监听器
            if (!img.dataset.previewInitialized) {
                img.dataset.previewInitialized = 'true';
                img.addEventListener('mouseenter', handleMouseEnter);
                img.addEventListener('mouseleave', handleMouseLeave);
                console.log('JavDB美化: 为图片添加hover事件', img.src);
            }
        });

        console.log('JavDB美化: 图片预览功能初始化完成');

        function handleMouseEnter(e) {
            const img = e.target;

            // 从小图URL获取大图URL
            const smallImageUrl = img.src;
            const largeImageUrl = smallImageUrl.replace('_s_', '_l_');

            console.log('JavDB美化: 显示预览', smallImageUrl, '->', largeImageUrl);

            // 立即显示预览容器（先显示一个占位）
            const rect = img.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            // 水平居中
            let left = rect.left + (rect.width / 2) - 300; // 600px / 2 = 300px
            if (left < 10) left = 10;
            if (left + 600 > viewportWidth - 10) left = viewportWidth - 610;

            // 垂直位置计算：确保不遮挡原图
            let top;
            const spaceAbove = rect.top;
            const spaceBelow = viewportHeight - rect.bottom;

            // 弹窗最大高度（包含padding）
            const maxPopupHeight = 600 + 20; // 600px图片 + 10px上下padding
            const margin = 15; // 与原图的间距

            if (spaceAbove >= maxPopupHeight + margin) {
                // 上方有足够空间，显示在上方
                top = rect.top - maxPopupHeight - margin;
            } else if (spaceBelow >= maxPopupHeight + margin) {
                // 下方有足够空间，显示在下方
                top = rect.bottom + margin;
            } else {
                // 两边空间都不够，选择空间较大的一边
                if (spaceAbove > spaceBelow) {
                    // 上方空间较大，贴着顶部显示
                    top = 10;
                } else {
                    // 下方空间较大，贴着底部显示
                    top = viewportHeight - maxPopupHeight - 10;
                }
            }

            // 确保不超出视口边界
            if (top < 10) top = 10;
            if (top > viewportHeight - 100) top = viewportHeight - 100;

            console.log('JavDB美化: 设置位置', {
                left, top, rect,
                viewportWidth, viewportHeight,
                scrollY: window.scrollY
            });

            // 强制设置样式
            previewContainer.style.setProperty('left', left + 'px', 'important');
            previewContainer.style.setProperty('top', top + 'px', 'important');
            previewContainer.style.setProperty('display', 'block', 'important');
            previewContainer.style.setProperty('opacity', '1', 'important');

            // 设置预览图片
            previewImg.onload = () => {
                console.log('JavDB美化: 大图加载成功');
            };
            previewImg.onerror = () => {
                console.error('JavDB美化: 大图加载失败', largeImageUrl);
                // 显示错误信息
                previewImg.alt = '图片加载失败';
                previewContainer.style.setProperty('background', 'rgba(255, 0, 0, 0.8)', 'important');
            };
            previewImg.src = largeImageUrl;

            console.log('JavDB美化: 预览窗口应该已显示', {
                element: previewContainer,
                computed: window.getComputedStyle(previewContainer),
                boundingRect: previewContainer.getBoundingClientRect()
            });
        }

        function handleMouseLeave() {
            console.log('JavDB美化: 隐藏预览窗口');
            previewContainer.style.setProperty('opacity', '0', 'important');
            setTimeout(() => {
                if (previewContainer.style.opacity === '0') {
                    previewContainer.style.setProperty('display', 'none', 'important');
                    console.log('JavDB美化: 预览窗口已隐藏');
                }
            }, 300);
        }
    }

    // 应用样式和功能的统一函数
    function applyStyles() {
        // 检查是否已存在样式元素，避免重复注入
        if (!document.getElementById("javdb-main-style")) {
            // 使用GM_addStyle统一注入主样式
            try {
                GM_addStyle(customStyles);
                console.log("JavDB美化: GM_addStyle样式已应用");
            } catch (error) {
                // 如果GM_addStyle失败，则使用备用方式
                console.warn("JavDB美化: GM_addStyle失败，使用备用注入方式", error);
                const styleEl = document.createElement("style");
                styleEl.id = "javdb-main-style";
                styleEl.textContent = customStyles;
                document.head.appendChild(styleEl);
            }
        }

        // 应用DOM修改
        autoExpandContent();
        replaceCommasInMeta();
        hideMaleElements();

        // 延迟初始化图片预览，避免阻塞主线程
        setTimeout(() => {
            initImagePreview();
        }, 500);
    }

    // 根据DOM就绪状态决定何时应用样式
    function init() {
        if (document.readyState === "loading") {
            document.addEventListener("DOMContentLoaded", () => {
                applyStyles();
                // 恢复页面可见性
                document.body.classList.add('script-ready');
            });
        } else {
            applyStyles();
            // 恢复页面可见性
            document.body.classList.add('script-ready');
        }
    }

    // 初始化脚本
    init();

    // 监控DOM变化，确保样式和功能在动态内容上也能应用
    const observer = new MutationObserver((mutationsList) => {
        let needsUpdate = false;
        for (const mutation of mutationsList) {
            if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
                // 检查是否有实际的内容变化，避免因为我们自己的DOM操作触发无限循环
                for (const node of mutation.addedNodes) {
                    if (node.nodeType === Node.ELEMENT_NODE &&
                        !node.id?.includes('javdb-') &&
                        !node.classList?.contains('javdb-')) {
                        needsUpdate = true;
                        break;
                    }
                }
                if (needsUpdate) break;
            }
        }

        if (needsUpdate) {
            // 使用防抖避免频繁执行
            clearTimeout(observer.debounceTimer);
            observer.debounceTimer = setTimeout(() => {
                replaceCommasInMeta();
                hideMaleElements();
                // 只在没有预览窗口时才初始化
                if (!document.getElementById('javdb-image-preview')) {
                    initImagePreview();
                }
            }, 100);
        }
    });

    // 开始观察整个文档
    observer.observe(document.documentElement, { childList: true, subtree: true });
})();
